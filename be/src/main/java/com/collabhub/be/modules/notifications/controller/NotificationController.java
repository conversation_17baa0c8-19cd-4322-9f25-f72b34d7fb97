package com.collabhub.be.modules.notifications.controller;

import com.collabhub.be.modules.auth.dto.UserType;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.notifications.dto.MarkNotificationReadRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageResponse;
import com.collabhub.be.modules.notifications.service.ExternalNotificationManagementService;
import com.collabhub.be.modules.notifications.service.NotificationManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Unified REST controller for notification operations that automatically handles both internal and external users.
 * 
 * <p>This controller provides a single set of endpoints that work for both internal users (with user IDs) 
 * and external users (with email-only identification). It automatically detects the user type from the JWT 
 * token and routes to the appropriate service implementation.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>Automatic user type detection from JWT claims</li>
 *   <li>Unified API endpoints for both user types</li>
 *   <li>Proper security validation for each user type</li>
 *   <li>Consistent error handling and responses</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/notifications")
@Tag(name = "Notifications", description = "Unified notification management for both internal and external users")
public class NotificationController {

    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);

    private final NotificationManagementService internalNotificationService;
    private final ExternalNotificationManagementService externalNotificationService;
    private final JwtClaimsService jwtClaimsService;

    public NotificationController(NotificationManagementService internalNotificationService,
                                ExternalNotificationManagementService externalNotificationService,
                                JwtClaimsService jwtClaimsService) {
        this.internalNotificationService = internalNotificationService;
        this.externalNotificationService = externalNotificationService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Retrieves notifications for the current user (internal or external) with pagination.
     *
     * @param page the page number (0-based)
     * @param size the page size
     * @param unreadOnly whether to return only unread notifications
     * @return page of notifications
     */
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Get user notifications (unified)",
        description = "Retrieves paginated notifications for the authenticated user. Automatically handles both internal and external users."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid pagination parameters"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "500", description = "Internal server error during notification retrieval")
    })
    public ResponseEntity<NotificationPageResponse> getNotifications(
            @Parameter(description = "Page number (0-based, min: 0, max: 1000)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size (min: 1, max: 100)", example = "20")
            @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Return only unread notifications", example = "false")
            @RequestParam(defaultValue = "false") boolean unreadOnly) {

        NotificationPageRequest pageRequest = NotificationPageRequest.of(page, size, unreadOnly);
        
        // Detect user type and route to appropriate service
        UserType userType = jwtClaimsService.getCurrentUser().getUserType();
        
        if (userType == UserType.INTERNAL) {
            logger.debug("Routing to internal notification service for user type: {}", userType);
            NotificationPageResponse notifications = internalNotificationService.getUserNotifications(pageRequest);
            return ResponseEntity.ok(notifications);
        } else {
            logger.debug("Routing to external notification service for user type: {}", userType);
            String email = jwtClaimsService.getCurrentUser().getEmail();
            NotificationPageResponse notifications = externalNotificationService.getExternalUserNotifications(email, pageRequest);
            return ResponseEntity.ok(notifications);
        }
    }

    /**
     * Marks a specific notification as read (internal users only).
     *
     * @param request the mark as read request
     * @return success response
     */
    @PostMapping("/mark-read")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Mark notification as read",
        description = "Marks a specific notification as read. Only available for internal users."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification marked as read successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or operation not supported for external users"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "404", description = "Notification not found or not owned by the authenticated user"),
            @ApiResponse(responseCode = "500", description = "Internal server error during notification update")
    })
    public ResponseEntity<Void> markNotificationAsRead(@Valid @RequestBody MarkNotificationReadRequest request) {
        
        UserType userType = jwtClaimsService.getCurrentUser().getUserType();
        
        if (userType != UserType.INTERNAL) {
            logger.warn("External user attempted to mark individual notification as read - operation not supported");
            return ResponseEntity.badRequest().build();
        }
        
        internalNotificationService.markNotificationAsRead(request.getNotificationId());
        return ResponseEntity.ok().build();
    }

    /**
     * Marks all notifications as read for the current user (internal or external).
     *
     * @return number of notifications marked as read
     */
    @PostMapping("/mark-all-read")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Mark all notifications as read (unified)",
        description = "Marks all unread notifications as read for the authenticated user. Works for both internal and external users."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "All notifications marked as read successfully - returns count of updated notifications"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "500", description = "Internal server error during bulk notification update")
    })
    public ResponseEntity<Integer> markAllNotificationsAsRead() {
        
        UserType userType = jwtClaimsService.getCurrentUser().getUserType();
        
        if (userType == UserType.INTERNAL) {
            logger.debug("Marking all notifications as read for internal user");
            int updatedCount = internalNotificationService.markAllNotificationsAsRead();
            return ResponseEntity.ok(updatedCount);
        } else {
            logger.debug("Marking all notifications as read for external user");
            String email = jwtClaimsService.getCurrentUser().getEmail();
            int updatedCount = externalNotificationService.markAllNotificationsAsRead(email);
            return ResponseEntity.ok(updatedCount);
        }
    }

    /**
     * Gets the count of unread notifications for the current user (internal or external).
     *
     * @return number of unread notifications
     */
    @GetMapping("/unread-count")
    @PreAuthorize("isAuthenticated()")
    @Operation(
        summary = "Get unread notification count (unified)",
        description = "Retrieves the total count of unread notifications for the authenticated user. Works for both internal and external users."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Unread count retrieved successfully - returns non-negative integer"),
            @ApiResponse(responseCode = "401", description = "Authentication required - missing or invalid JWT token"),
            @ApiResponse(responseCode = "500", description = "Internal server error during count retrieval")
    })
    public ResponseEntity<Integer> getUnreadNotificationCount() {
        
        UserType userType = jwtClaimsService.getCurrentUser().getUserType();
        
        if (userType == UserType.INTERNAL) {
            logger.debug("Getting unread count for internal user");
            int count = internalNotificationService.getUnreadNotificationCount();
            return ResponseEntity.ok(count);
        } else {
            logger.debug("Getting unread count for external user");
            String email = jwtClaimsService.getCurrentUser().getEmail();
            int count = externalNotificationService.getUnreadNotificationCount(email);
            return ResponseEntity.ok(count);
        }
    }
}
