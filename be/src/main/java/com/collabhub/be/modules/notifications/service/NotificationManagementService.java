package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.notifications.constants.NotificationConstants;
import com.collabhub.be.modules.notifications.converter.NotificationConverter;
import com.collabhub.be.modules.notifications.dto.NotificationPageRequest;
import com.collabhub.be.modules.notifications.dto.NotificationPageResponse;
import com.collabhub.be.modules.notifications.dto.NotificationResponse;
import com.collabhub.be.modules.notifications.exception.NotificationNotFoundException;
import com.collabhub.be.modules.notifications.repository.NotificationRepository;
import org.jooq.generated.tables.pojos.Notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;

/**
 * Production-grade service for managing user notifications in the application.
 *
 * <p>This service handles notification retrieval, status updates, and pagination for in-app
 * notifications with comprehensive validation, error handling, and performance optimizations.
 * It provides user-scoped access to notifications with proper security controls.</p>
 *
 * <h3>Key Features:</h3>
 * <ul>
 *   <li>0-based pagination with {@link NotificationPageRequest}</li>
 *   <li>User-scoped notification access with security validation</li>
 *   <li>Bulk operations for marking notifications as read</li>
 *   <li>Comprehensive validation and error handling</li>
 *   <li>Performance-optimized queries with proper indexing</li>
 * </ul>
 *
 * <AUTHOR> Hub Team
 * @since 1.0.0
 */
@Service
@Validated
public class NotificationManagementService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationManagementService.class);

    // Constants
    private static final String RETRIEVE_NOTIFICATIONS_MESSAGE = "Retrieving notifications for user: {} (page={}, size={}, unreadOnly={})";
    private static final String RETRIEVED_NOTIFICATIONS_MESSAGE = "Retrieved {} notifications for user {} (total: {})";
    private static final String MARK_READ_MESSAGE = "Marking notification as read: id={}, user={}";
    private static final String MARK_READ_SUCCESS_MESSAGE = "Marked notification as read: id={}, user={}";
    private static final String MARK_READ_FAILED_MESSAGE = "Failed to mark notification as read - not found or not owned: id={}, user={}";
    private static final String MARK_ALL_READ_MESSAGE = "Marking all notifications as read for user: {}";
    private static final String MARK_ALL_READ_SUCCESS_MESSAGE = "Marked {} notifications as read for user {}";
    private static final String UNREAD_COUNT_MESSAGE = "Getting unread notification count for user: {}";
    private static final String UNREAD_COUNT_RESULT_MESSAGE = "User {} has {} unread notifications";

    private final NotificationRepository notificationRepository;
    private final NotificationConverter notificationConverter;
    private final JwtClaimsService jwtClaimsService;

    public NotificationManagementService(NotificationRepository notificationRepository,
                                       NotificationConverter notificationConverter,
                                       JwtClaimsService jwtClaimsService) {
        this.notificationRepository = notificationRepository;
        this.notificationConverter = notificationConverter;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Retrieves notifications for the current user with pagination.
     *
     * <p>This method provides paginated access to user notifications with optional filtering
     * for unread notifications only. It uses 0-based pagination and includes total count
     * information for proper pagination controls.</p>
     *
     * @param pageRequest the page request with pagination and filtering parameters (must not be null)
     * @return page of notifications with pagination metadata
     *
     * @throws IllegalArgumentException if pageRequest is invalid
     */
    @Transactional(readOnly = true)
    public NotificationPageResponse getUserNotifications(@NotNull @Valid NotificationPageRequest pageRequest) {

        Long userId = getCurrentUserId();
        logRetrievalRequest(userId, pageRequest);

        List<Notification> notifications = fetchNotifications(userId, pageRequest);
        int totalCount = getTotalCount(userId, pageRequest.isUnreadOnly());
        List<NotificationResponse> responseList = convertToResponseList(notifications);

        logRetrievalResult(userId, responseList, totalCount);
        return createPageResponse(responseList, pageRequest, totalCount);
    }

    /**
     * Gets the current user ID from JWT claims.
     *
     * @return the current user ID
     */
    private Long getCurrentUserId() {
        return jwtClaimsService.getCurrentUser().getUserId();
    }

    /**
     * Logs the notification retrieval request.
     */
    private void logRetrievalRequest(Long userId, NotificationPageRequest pageRequest) {
        logger.debug(RETRIEVE_NOTIFICATIONS_MESSAGE, userId,
                    pageRequest.getPage(), pageRequest.getSize(), pageRequest.isUnreadOnly());
    }

    /**
     * Fetches notifications from the repository with pagination.
     */
    private List<Notification> fetchNotifications(Long userId, NotificationPageRequest pageRequest) {
        return notificationRepository.findByUserIdWithPagination(
                userId, pageRequest.getPage(), pageRequest.getSize(), pageRequest.isUnreadOnly());
    }

    /**
     * Gets the total count of notifications for pagination.
     */
    private int getTotalCount(Long userId, boolean unreadOnly) {
        return notificationRepository.countByUserId(userId, unreadOnly);
    }

    /**
     * Converts notification entities to response DTOs.
     */
    private List<NotificationResponse> convertToResponseList(List<Notification> notifications) {
        return notificationConverter.toResponseList(notifications);
    }

    /**
     * Logs the notification retrieval result.
     */
    private void logRetrievalResult(Long userId, List<NotificationResponse> responseList, int totalCount) {
        logger.info(RETRIEVED_NOTIFICATIONS_MESSAGE, responseList.size(), userId, totalCount);
    }

    /**
     * Creates the paginated response.
     */
    private NotificationPageResponse createPageResponse(List<NotificationResponse> responseList,
                                                       NotificationPageRequest pageRequest, int totalCount) {
        return NotificationPageResponse.of(responseList, pageRequest, totalCount);
    }

    /**
     * Marks a specific notification as read.
     *
     * <p>This method marks a notification as read for the current user, ensuring that
     * users can only mark their own notifications as read. If the notification is not
     * found or doesn't belong to the user, an exception is thrown.</p>
     *
     * @param notificationId the notification ID (must be positive)
     *
     * @throws IllegalArgumentException if notificationId is null or non-positive
     * @throws NotificationNotFoundException if notification is not found or not owned by user
     */
    @Transactional
    public void markNotificationAsRead(@NotNull @Positive Long notificationId) {

        Long userId = getCurrentUserId();
        logger.debug(MARK_READ_MESSAGE, notificationId, userId);

        boolean updated = attemptMarkAsRead(notificationId, userId);
        handleMarkAsReadResult(updated, notificationId, userId);
    }

    /**
     * Attempts to mark a notification as read.
     *
     * @param notificationId the notification ID
     * @param userId the user ID
     * @return true if the notification was successfully marked as read
     */
    private boolean attemptMarkAsRead(Long notificationId, Long userId) {
        return notificationRepository.markAsRead(notificationId, userId);
    }

    /**
     * Handles the result of marking a notification as read.
     *
     * @param updated whether the notification was successfully updated
     * @param notificationId the notification ID
     * @param userId the user ID
     * @throws NotificationNotFoundException if the notification was not found or not owned
     */
    private void handleMarkAsReadResult(boolean updated, Long notificationId, Long userId) {
        if (updated) {
            logger.info(MARK_READ_SUCCESS_MESSAGE, notificationId, userId);
        } else {
            logger.warn(MARK_READ_FAILED_MESSAGE, notificationId, userId);
            throw new NotificationNotFoundException("Notification not found or not owned by user: " + notificationId);
        }
    }

    /**
     * Marks all notifications as read for the current user.
     *
     * <p>This method performs a bulk update operation to mark all unread notifications
     * as read for the current user. It returns the number of notifications that were
     * actually updated, which may be zero if all notifications were already read.</p>
     *
     * @return number of notifications marked as read (non-negative)
     */
    @Transactional
    public int markAllNotificationsAsRead() {

        Long userId = getCurrentUserId();
        logger.debug(MARK_ALL_READ_MESSAGE, userId);

        int updatedCount = performBulkMarkAsRead(userId);
        logBulkMarkAsReadResult(userId, updatedCount);

        return updatedCount;
    }

    /**
     * Performs the bulk mark as read operation.
     *
     * @param userId the user ID
     * @return number of notifications marked as read
     */
    private int performBulkMarkAsRead(Long userId) {
        return notificationRepository.markAllAsRead(userId);
    }

    /**
     * Logs the result of bulk mark as read operation.
     */
    private void logBulkMarkAsReadResult(Long userId, int updatedCount) {
        logger.info(MARK_ALL_READ_SUCCESS_MESSAGE, updatedCount, userId);
    }

    /**
     * Gets the count of unread notifications for the current user.
     *
     * <p>This method provides a fast count of unread notifications for the current user,
     * which is commonly used for displaying notification badges in the UI.</p>
     *
     * @return number of unread notifications (non-negative)
     */
    @Transactional(readOnly = true)
    public int getUnreadNotificationCount() {

        Long userId = getCurrentUserId();
        logger.debug(UNREAD_COUNT_MESSAGE, userId);

        int count = getUnreadCount(userId);
        logUnreadCountResult(userId, count);

        return count;
    }

    /**
     * Gets the unread count from the repository.
     *
     * @param userId the user ID
     * @return the unread count
     */
    private int getUnreadCount(Long userId) {
        return notificationRepository.countByUserId(userId, true);
    }

    /**
     * Logs the unread count result.
     */
    private void logUnreadCountResult(Long userId, int count) {
        logger.info(UNREAD_COUNT_RESULT_MESSAGE, userId, count);
    }
}
