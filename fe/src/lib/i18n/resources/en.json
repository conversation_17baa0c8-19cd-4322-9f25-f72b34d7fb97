{"auth": {"login": {"title": "Login to your account", "description": "Enter your email below to login to your account", "email": "Email", "password": "Password", "forgotPassword": "Forgot your password?", "submit": "<PERSON><PERSON>", "submitting": "Logging in...", "error": "<PERSON><PERSON> failed. Please check your credentials and try again.", "noAccount": "Don't have an account?", "signUp": "Sign up"}, "register": {"title": "Create your account", "description": "Enter your information below to create your account", "accountName": "Account Name", "displayName": "Full Name", "email": "Email", "password": "Password", "passwordHint": "Must contain at least 8 characters with uppercase, lowercase, number, and special character", "submit": "Create Account", "submitting": "Creating Account...", "error": "Registration failed. Please try again.", "hasAccount": "Already have an account?", "signIn": "Sign in", "success": {"title": "Registration Successful", "description": "Please check your email to verify your account", "checkEmail": "Registration successful. Please check your email to verify your account.", "registerAnother": "Register Another Account", "goToLogin": "Go to Login"}}, "magicLink": {"authenticating": "Authenticating...", "processing": "Processing your invitation...", "invalidLink": "Invalid Magic Link", "missingToken": "The magic link appears to be incomplete or corrupted.", "linkExpired": "Link Expired", "linkExpiredDescription": "This magic link has expired. Please request a new invitation.", "authenticationFailed": "Authentication Failed", "authenticationFailedDescription": "We couldn't authenticate you with this link. Please try again or request a new invitation.", "welcomeTitle": "Welcome to the Collaboration!", "welcomeBackTitle": "Welcome Back!", "firstAccessMessage": "You've successfully joined the collaboration hub. Redirecting you now...", "returningMessage": "Welcome back to the collaboration hub. Redirecting you now...", "tryAgain": "Try Again", "goToLogin": "Go to Login"}, "initializing": "Initializing authentication...", "verifying": "Verifying authentication..."}, "validation": {"required": "This field is required", "email": {"invalid": "Email must be valid", "tooLong": "Email must not exceed 255 characters"}, "password": {"tooShort": "Password must be at least 8 characters", "tooLong": "Password must not exceed 255 characters", "requirements": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"}, "accountName": {"tooShort": "Account name must be at least 2 characters", "tooLong": "Account name must not exceed 100 characters"}, "displayName": {"required": "Display name is required", "tooLong": "Display name must not exceed 255 characters"}}, "placeholders": {"accountName": "Your Company or Organization", "displayName": "<PERSON>", "email": "<EMAIL>", "password": "Enter a strong password"}, "messages": {"emailSentTo": "We've sent a verification email to:"}, "accountCompanies": {"title": "Account Companies", "description": "Manage your business partners and client companies", "createNew": "Create New Company", "noCompanies": "No companies found", "noCompaniesDescription": "Get started by creating your first company", "loading": "Loading companies...", "error": "Failed to load companies", "edit": "Edit", "delete": "Delete", "confirmDelete": "Delete Company", "deleteDescription": "Are you sure you want to delete this company? This action cannot be undone.", "deleting": "Deleting...", "creating": "Creating...", "updating": "Updating...", "createTitle": "Create New Company", "editTitle": "Edit Company", "save": "Save Changes", "cancel": "Cancel", "createSuccess": "Company created successfully", "createError": "Failed to create company", "companyName": "Company Name", "email": "Email", "phone": "Phone", "website": "Website", "addressStreet": "Street Address", "addressCity": "City", "addressPostalCode": "Postal Code", "addressCountry": "Country", "vatNumber": "VAT Number", "registrationNumber": "Registration Number", "required": "Required", "validation": {"companyNameRequired": "Company name is required", "companyNameTooLong": "Company name must not exceed 255 characters", "emailInvalid": "Email must be valid", "emailTooLong": "Email must not exceed 255 characters", "phoneTooLong": "Phone must not exceed 50 characters", "websiteTooLong": "Website must not exceed 500 characters", "addressStreetTooLong": "Street address must not exceed 255 characters", "addressCityTooLong": "City must not exceed 100 characters", "addressPostalCodeTooLong": "Postal code must not exceed 20 characters", "addressCountryTooLong": "Country must not exceed 100 characters", "vatNumberTooLong": "VAT number must not exceed 50 characters", "registrationNumberTooLong": "Registration number must not exceed 100 characters"}}, "bankDetails": {"title": "Bank Details", "description": "Manage your bank account information for invoicing and payments", "createNew": "Add Bank Detail", "noBankDetails": "No bank details found", "noBankDetailsDescription": "Add your first bank account to start managing payment information for invoices.", "loading": "Loading bank details...", "error": "Failed to load bank details", "edit": "Edit", "delete": "Delete", "confirmDelete": "Delete Bank Detail", "deleteDescription": "Are you sure you want to delete this bank detail? This action cannot be undone.", "deleting": "Deleting...", "creating": "Creating...", "updating": "Updating...", "createTitle": "Add New Bank Detail", "editTitle": "Edit Bank Detail", "save": "Save", "cancel": "Cancel", "createSuccess": "Bank detail created successfully", "createError": "Failed to create bank detail", "updateSuccess": "Bank detail updated successfully", "updateError": "Failed to update bank detail", "deleteSuccess": "Bank detail deleted successfully", "deleteError": "Failed to delete bank detail", "name": "Account Name", "bankName": "Bank Name", "iban": "IBAN", "bicSwift": "BIC/SWIFT Code", "required": "Required", "validation": {"nameRequired": "Account name is required", "nameTooLong": "Account name must not exceed 255 characters", "bankNameTooLong": "Bank name must not exceed 255 characters", "ibanInvalid": "IBAN format is invalid", "ibanTooShort": "IBAN must be at least 15 characters", "ibanTooLong": "IBAN must not exceed 34 characters", "bicSwiftInvalid": "BIC/SWIFT format is invalid", "bicSwiftTooShort": "BIC/SWIFT must be at least 8 characters", "bicSwiftTooLong": "BIC/SWIFT must not exceed 11 characters"}}, "invoices": {"title": "Invoices", "description": "Description", "createNew": "Create New Invoice", "noInvoices": "No invoices found", "noInvoicesDescription": "Get started by creating your first invoice", "loading": "Loading invoices...", "error": "Failed to load invoices", "edit": "Edit", "delete": "Delete", "send": "Send", "download": "Download PDF", "viewDetails": "View Details", "confirmDelete": "Delete Invoice", "deleteDescription": "Are you sure you want to delete this invoice? This action cannot be undone.", "deleting": "Deleting...", "creating": "Creating...", "updating": "Updating...", "sending": "Sending...", "createTitle": "Create New Invoice", "editTitle": "Edit Invoice", "detailTitle": "Invoice Details", "save": "Save Invoice", "cancel": "Cancel", "invoiceNumber": "Invoice Number", "status": {"draft": "Draft", "sent": "<PERSON><PERSON>", "paid": "Paid", "overdue": "Overdue"}, "issueDate": "Issue Date", "dueDate": "Due Date", "currency": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Notes", "subtotal": "Subtotal", "totalVat": "Total VAT", "totalAmount": "Total Amount", "recipient": "Recipient", "issuer": "Issuer", "bankDetails": "Bank Details", "lineItems": "Line Items", "recipients": "Recipients", "addItem": "Add Item", "addRecipient": "Add Recipient", "removeItem": "Remove Item", "removeRecipient": "Remove Recipient", "quantity": "Quantity", "unitPrice": "Unit Price", "vatRate": "VAT Rate", "lineTotal": "Line Total", "email": "Email", "recipientType": "Type", "primary": "Primary", "cc": "CC", "overdue": "Overdue", "daysOverdue": "{{days}} days overdue", "dueIn": "Due in {{days}} days", "dueToday": "Due today", "page": "Page", "of": "of", "previous": "Previous", "next": "Next", "filters": {"title": "Filters", "status": "Status", "dateRange": "Date Range", "fromDate": "From Date", "toDate": "To Date", "clearAll": "Clear All", "apply": "Apply Filters", "activeFilters": "Active filters:"}, "statusUpdate": {"title": "Update Invoice Status", "currentStatus": "Current Status", "newStatus": "New Status", "note": "Note (optional)", "update": "Update Status", "updating": "Updating..."}, "sendDialog": {"title": "Send Invoice", "description": "Send this invoice via email to all recipients", "recipientsList": "Recipients", "confirmSend": "Send Invoice", "success": "Invoice sent successfully", "error": "Failed to send invoice"}, "sendConfirmation": {"confirmSendTitle": "Send Invoice", "confirmSendDescription": "Are you sure you want to send invoice {{invoiceNumber}} to all recipients?", "confirmResendTitle": "Resend Invoice", "confirmResendDescription": "Invoice {{invoiceNumber}} has already been sent. Are you sure you want to send it again?", "alreadySentWarning": "This invoice has already been sent successfully. Sending again will create duplicate emails for recipients.", "send": "Send Invoice", "forceSend": "Force Send", "cancel": "Cancel"}, "viewDialog": {"title": "Invoice Details", "basicInfo": "Basic Information", "lineItems": "Line Items", "noLineItems": "No line items found", "recipients": "Recipients", "totals": "Totals", "entityIds": "Entity References", "issuerInfo": "Issuer Information", "recipientInfo": "Recipient Information", "bankDetailsInfo": "Bank Details Information", "loading": "Loading invoice details...", "error": "Failed to load invoice details", "close": "Close", "downloadPdf": "Download PDF", "companyName": "Company Name", "name": "Name", "address": "Address", "contacts": "Contacts", "accountName": "Account Name", "bankName": "Bank Name", "vatAmount": "VAT Amount", "recipientLabels": {"type": {"original": "Primary", "copy": "CC"}, "source": {"brand_contact": "Brand Contact", "manual": "Manual Entry"}, "sendCount": "Sent {{count}} time{{plural}}", "lastSent": "Last sent", "noRecipients": "No recipients found"}}, "form": {"basicInfo": "Basic Information", "clientInfo": "Client Information", "lineItems": "Line Items", "recipients": "Recipients", "totals": "Totals", "additionalDetails": "Additional Details", "invoiceNumber": "Invoice Number", "generateInvoiceNumber": "Generate invoice number", "generatingInvoiceNumber": "Generating...", "currency": "<PERSON><PERSON><PERSON><PERSON>", "issueDate": "Issue Date", "dueDate": "Due Date", "issuer": "Issuer", "recipient": "Recipient", "bankDetails": "Bank Details", "myBankDetails": "My Bank Details", "notes": "Notes", "noIssuersFound": "No issuers found", "noBrandsFound": "No brands found", "noBankDetailsFound": "No bank details found", "optional": "Optional", "description": "Description", "quantity": "Quantity", "unitPrice": "Unit Price", "vatRate": "VAT Rate", "lineTotal": "Line Total", "lineItem": "Line Item", "addItem": "Add Item", "email": "Email Address", "recipientType": "Recipient Type", "addRecipient": "Add Recipient", "createTitle": "Create New Invoice", "editTitle": "Edit Invoice", "createInvoice": "Create Invoice", "updateInvoice": "Update Invoice", "creating": "Creating...", "updating": "Updating...", "cancel": "Cancel", "close": "Close", "readOnlyMessage": "This invoice cannot be edited because it has already been sent. Only draft invoices can be modified.", "loadingInvoice": "Loading invoice data...", "error": "Failed to save invoice", "placeholders": {"invoiceNumber": "e.g., INV-2024-001", "selectCurrency": "Select currency...", "selectIssuer": "Select issuer company...", "selectRecipient": "Select recipient company...", "selectBankDetails": "Select bank details...", "searchIssuers": "Search issuers...", "searchBrands": "Search brands...", "searchBankDetails": "Search bank details...", "notes": "Add any additional notes or comments...", "itemDescription": "Describe the service or product...", "recipientEmail": "<EMAIL>", "selectRecipientType": "Select type...", "selectIssueDate": "Select issue date...", "selectDueDate": "Select due date..."}, "validation": {"issuerRequired": "Issuer is required", "recipientRequired": "Recipient is required", "bankDetailsOptional": "Bank details are optional", "invoiceNumberRequired": "Invoice number is required", "invoiceNumberTooLong": "Invoice number cannot exceed 50 characters", "currencyRequired": "Currency is required", "issueDateRequired": "Issue date is required", "dueDateRequired": "Due date is required", "dueDateAfterIssue": "Due date must be after issue date", "notesTooLong": "Notes cannot exceed 2000 characters", "itemsRequired": "At least one line item is required", "itemDescriptionRequired": "Description is required", "itemDescriptionTooLong": "Description cannot exceed 500 characters", "itemQuantityRequired": "Quantity is required", "itemQuantityMin": "Quantity must be greater than 0", "itemUnitPriceRequired": "Unit price is required", "itemUnitPriceMin": "Unit price must be 0 or greater", "itemVatRateRequired": "VAT rate is required", "itemVatRateMin": "VAT rate must be 0 or greater", "itemVatRateMax": "VAT rate must be 1 or less", "recipientsRequired": "At least one recipient is required", "emailRequired": "Email is required", "emailInvalid": "Email must be valid", "emailTooLong": "Email cannot exceed 255 characters", "recipientTypeRequired": "Recipient type is required"}}, "validation": {"invoiceNumberRequired": "Invoice number is required", "issueDateRequired": "Issue date is required", "dueDateRequired": "Due date is required", "currencyRequired": "Currency is required", "itemsRequired": "At least one line item is required", "recipientsRequired": "At least one recipient is required", "descriptionRequired": "Description is required", "quantityRequired": "Quantity is required", "quantityMin": "Quantity must be greater than 0", "unitPriceRequired": "Unit price is required", "unitPriceMin": "Unit price cannot be negative", "vatRateMin": "VAT rate cannot be negative", "vatRateMax": "VAT rate cannot exceed 100%", "emailRequired": "Email is required", "emailInvalid": "Email must be valid", "recipientTypeRequired": "Recipient type is required"}}, "brands": {"title": "Brands", "description": "Manage your client brands and their contact information", "createNew": "Create New Brand", "noBrands": "No brands found", "noBrandsDescription": "Get started by creating your first brand to manage client relationships and contacts.", "loading": "Loading brands...", "error": "Failed to load brands", "edit": "Edit", "delete": "Delete", "confirmDelete": "Delete Brand", "deleteDescription": "Are you sure you want to delete this brand? This will hide it from the platform and future invoices.", "deleting": "Deleting...", "creating": "Creating...", "updating": "Updating...", "createTitle": "Create New Brand", "editTitle": "Edit Brand", "save": "Save", "cancel": "Cancel", "createSuccess": "Brand created successfully", "createError": "Failed to create brand", "updateSuccess": "Brand updated successfully", "updateError": "Failed to update brand", "deleteSuccess": "Brand deleted successfully", "deleteError": "Failed to delete brand", "name": "Brand Name", "companyName": "Company Name", "email": "Email", "phone": "Phone", "website": "Website", "basicInfo": "Basic Information", "addressInfo": "Address Information", "businessInfo": "Business Information", "addressStreet": "Street Address", "addressCity": "City", "addressPostalCode": "Postal Code", "addressCountry": "Country", "vatNumber": "VAT Number", "registrationNumber": "Registration Number", "contacts": "Contacts", "contact": "Contact", "contactName": "Name", "contactEmail": "Email", "contactNotes": "Notes", "addContact": "Add Contact", "noContacts": "No contacts added yet", "noContactsDescription": "Add contacts to manage communication with this brand", "required": "Required", "searchPlaceholder": "Search brands...", "filterByName": "Filter by name", "showingResults": "Showing {{count}} of {{total}} brands", "noBrandsForSearch": "No brands found for \"{{searchTerm}}\"", "noBrandsForSearchDescription": "Try adjusting your search terms or create a new brand.", "page": "Page", "of": "of", "previous": "Previous", "next": "Next", "validation": {"nameRequired": "Brand name is required", "nameTooLong": "Brand name cannot exceed 255 characters", "companyNameRequired": "Company name is required", "companyNameTooLong": "Company name cannot exceed 255 characters", "emailInvalid": "Email must be valid", "emailTooLong": "Email cannot exceed 255 characters", "phoneTooLong": "Phone cannot exceed 50 characters", "websiteTooLong": "Website cannot exceed 500 characters", "addressStreetTooLong": "Street address cannot exceed 500 characters", "addressCityTooLong": "City cannot exceed 100 characters", "addressPostalCodeTooLong": "Postal code cannot exceed 20 characters", "addressCountryTooLong": "Country cannot exceed 100 characters", "vatNumberTooLong": "VAT number cannot exceed 50 characters", "registrationNumberTooLong": "Registration number cannot exceed 100 characters", "contactNameRequired": "Contact name is required", "contactNameTooLong": "Contact name cannot exceed 255 characters", "contactEmailRequired": "Contact email is required", "contactEmailInvalid": "Contact email must be valid", "contactEmailTooLong": "Contact email cannot exceed 255 characters", "contactNotesTooLong": "Contact notes cannot exceed 2000 characters"}}, "collaborationHubs": {"title": "Collaboration Hubs", "description": "Manage your collaboration hubs and content creation workflows", "createNew": "Create New Hub", "noHubs": "No collaboration hubs yet", "noHubsDescription": "Create your first collaboration hub to start managing content creation with your team and brand partners.", "noHubsForSearch": "No hubs found for \"{{searchTerm}}\"", "noHubsForSearchDescription": "Try adjusting your search terms or create a new hub.", "loading": "Loading hubs...", "error": "Failed to load collaboration hubs", "viewHub": "View Hub", "editHub": "Edit Hub", "deleteHub": "Delete Hub", "confirmDelete": "Delete Collaboration Hub", "deleteDescription": "Are you sure you want to delete this collaboration hub? This action cannot be undone.", "deleting": "Deleting...", "delete": "Delete", "cancel": "Cancel", "hubToDelete": "Hub to delete:", "deleteWarning": "⚠️ This action is permanent", "deleteWarningDetails": "All posts, comments, chat messages, briefs, and participant data will be permanently deleted.", "posts": {"title": "Post Details", "searchPlaceholder": "Search posts...", "filterByStatus": "Filter by status", "allPosts": "All Posts", "pendingReview": "Pending Review", "approved": "Approved", "needsRework": "Needs Rework", "createPost": "Create Post", "editPost": "Edit Post", "deletePost": "Delete Post", "likes": "likes", "comments": {"title": "Comments", "noComments": "No comments yet", "beFirstToComment": "Be the first to comment!", "writeComment": "Write a comment...", "postComment": "Post", "posting": "Posting...", "editComment": "Edit Comment", "deleteComment": "Delete Comment", "confirmDeleteComment": "Delete Comment", "deleteCommentDescription": "Are you sure you want to delete this comment? This action cannot be undone.", "deleting": "Deleting...", "commentDeleted": "Comment deleted successfully", "commentPosted": "Comment posted successfully", "commentUpdated": "Comment updated successfully", "loadMore": "Load more comments", "loading": "Loading...", "loadingComments": "Loading comments...", "failedToLoad": "Failed to load comments", "tryAgain": "Try again", "comment": "comment", "commentsPlural": "comments", "edited": "(edited)", "save": "Save", "cancel": "Cancel", "saving": "Saving...", "ctrlEnterToPost": "Press Ctrl+Enter to post", "edit": "Edit", "delete": "Delete", "failedToPost": "Failed to post comment. Please try again.", "failedToUpdate": "Failed to update comment. Please try again.", "failedToDelete": "Failed to delete comment. Please try again.", "ctrlEnterToSave": "Press Ctrl+Enter to save", "escapeToCancel": "Press Escape to cancel"}, "reviewers": "Reviewers:", "pending": "pending", "rework": "rework", "noPosts": "No posts yet", "noPostsDescription": "This collaboration hub doesn't have any posts yet. Create the first post to get started.", "createFirstPost": "Create First Post", "loading": "Loading posts...", "loadingMore": "Loading more posts...", "error": "Failed to load posts", "errorDescription": "There was an error loading the posts. Please try again.", "retry": "Retry", "createdBy": "Created by", "createdAt": "Created", "updatedAt": "Updated", "status": {"pending": "Pending Review", "approved": "Approved", "rework": "Needs Rework", "all": "All Statuses"}, "filters": {"types": {"all": "All Posts", "assignedToMe": "Assigned to Me", "needsReview": "Needs Review", "myPending": "My Pending", "myApproved": "My Approved", "myRework": "My Rework", "reviewedByMe": "Reviewed by Me"}}, "actions": {"edit": "Edit", "delete": "Delete", "view": "View"}, "confirmDelete": "Delete Post", "deleteDescription": "Are you sure you want to delete this post? This action cannot be undone.", "deleting": "Deleting...", "postDeleted": "Post deleted successfully", "failedToDelete": "Failed to delete post. Please try again.", "postCreated": "Post created successfully", "postUpdated": "Post updated successfully", "createPostDescription": "Create a new post with media content and assign reviewers for approval.", "editPostDescription": "Update your post content, media, and reviewer assignments.", "form": {"media": "Media", "mediaDescription": "Upload images (max 10MB) or videos (max 100MB). You can upload up to 10 files.", "caption": "Caption", "captionPlaceholder": "Write your post caption here...", "captionDescription": "characters", "reviewerNotes": "Notes for Reviewers", "reviewerNotesPlaceholder": "Add any specific instructions or context for reviewers...", "reviewerNotesDescription": "Optional notes that will be visible to reviewers during the approval process.", "reviewers": "Reviewers", "selectReviewers": "Select reviewers...", "reviewersDescription": "Choose which reviewers should approve this post. You can assign up to 10 reviewers.", "assignReviewers": "Assign Reviewers", "assignReviewersDescription": "Choose which reviewers should approve this post. You can assign up to 10 reviewers.", "cancel": "Cancel", "save": "Save", "creating": "Creating...", "updating": "Updating..."}, "review": {"reviewPost": "Review this post", "updateReview": "Update your review", "reviewDecision": "Review Decision", "approve": "Approve", "requestRework": "Request Rework", "reviewNotes": "Review Notes", "notesFromCreator": "Notes from Creator", "notesOptional": "Optional feedback", "notesRequiredForRework": "Please explain what needs to be changed", "notesPlaceholderApprove": "Optional: Add any positive feedback or notes...", "notesPlaceholderRework": "Explain what needs to be changed or improved...", "submitReview": "Submit Review", "submitting": "Submitting...", "reviewSubmitted": "Review submitted successfully", "reviewFailed": "Failed to submit review. Please try again.", "statusPending": "Pending", "statusApproved": "Approved", "statusRework": "Needs Rework", "reviewFeedback": "Review Feedback", "needsChanges": "Needs Changes", "feedback": "<PERSON><PERSON><PERSON>", "hasFeedback": "Has feedback", "withFeedback": "with feedback", "waitingForReview": "Waiting for review", "assignedReviewers": "Assigned Reviewers", "noReviewersAssigned": "No reviewers assigned", "showReviewers": "Show reviewers", "hideReviewers": "Hide reviewers", "viewReviewDetails": "View review details", "viewTimeline": "View timeline", "assigned": "Assigned", "reviewed": "Reviewed"}}, "hubName": "<PERSON>b Name", "brandName": "Brand", "myRole": "My Role", "createdAt": "Created", "searchPlaceholder": "Search hubs...", "filterByName": "Filter by name", "showingResults": "Showing {{count}} of {{total}} hubs", "page": "Page", "of": "of", "previous": "Previous", "next": "Next", "roles": {"admin": "Admin", "content_creator": "Content Creator", "reviewer": "Reviewer", "reviewer_creator": "Reviewer & Creator"}, "invite": "Invite", "unknown": "Unknown", "participant": "participant", "participants": "participants", "created": "Created", "updating": "Updating...", "updateSuccess": "Hub name updated successfully", "updateError": "Failed to update hub name", "open": "Open", "createDialog": {"title": "Create Collaboration Hub", "description": "Create a new collaboration hub to manage content creation and reviews with your team and brand partners.", "hubName": "<PERSON>b Name", "hubNameLabel": "<PERSON>b Name", "hubNamePlaceholder": "e.g., Summer Campaign 2025", "brand": "Brand", "brandLabel": "Brand", "brandPlaceholder": "Select a brand", "selectBrand": "Select a brand", "descriptionLabel": "Description (Optional)", "descriptionPlaceholder": "Describe the purpose of this collaboration hub...", "cancel": "Cancel", "creating": "Creating...", "createHub": "Create Hub", "successMessage": "Collaboration hub created successfully", "errorMessage": "Failed to create collaboration hub", "validation": {"hubNameRequired": "Hub name is required", "hubNameTooLong": "Hub name must not exceed 255 characters", "brandRequired": "Brand is required", "descriptionTooLong": "Description must not exceed 1000 characters"}}, "editDialog": {"title": "Edit Collaboration Hub", "description": "Update the collaboration hub details.", "updating": "Updating...", "updateHub": "Update Hub", "successMessage": "Collaboration hub updated successfully", "errorMessage": "Failed to update collaboration hub"}, "tabs": {"posts": "Posts", "chat": "Cha<PERSON>", "briefs": "Briefs", "overview": "Overview"}, "chat": {"channels": "Channels", "participants": "participants", "viewMembers": "View Members", "messagePlaceholder": "Message #{{channelName}}", "send": "Send", "general": "General", "adminsOnly": "Admins Only", "contentReview": "Content Review", "workspace": "Workspace", "loading": "Loading chat...", "error": "Failed to load chat", "noMessages": "No messages yet", "noMessagesDescription": "Start the conversation by sending the first message.", "typing": "{{name}} is typing...", "edited": "edited", "editMessage": "Edit message", "deleteMessage": "Delete message", "confirmDelete": "Delete Message", "deleteDescription": "Are you sure you want to delete this message? This action cannot be undone.", "uploading": "Uploading file...", "uploadError": "Failed to upload file", "attachFile": "Attach file", "addEmoji": "Add emoji", "mentionSomeone": "Mention someone", "sendingMessage": "Sending...", "messageError": "Failed to send message", "retryMessage": "Retry", "loadMore": "Load more messages", "loadingMessages": "Loading messages...", "connectionError": "Connection lost", "reconnecting": "Reconnecting...", "connected": "Connected", "disconnected": "Disconnected", "members": "Members", "failedToLoadMembers": "Failed to load members", "noMembers": "No members found", "createChannel": "Create Channel", "createCustomChannel": "Create Custom Channel", "channelName": "Channel Name", "channelDescription": "Description (optional)", "selectParticipants": "Select Participants", "creating": "Creating...", "create": "Create", "editChannel": "Edit Channel", "deleteChannel": "Delete Channel", "manageParticipants": "Manage Participants", "channelSettings": "Channel Settings", "confirmDeleteChannel": "Delete Channel", "deleteChannelDescription": "Are you sure you want to delete this channel? All messages will be permanently deleted.", "channelNamePlaceholder": "Enter channel name...", "channelDescriptionPlaceholder": "Enter channel description...", "noParticipantsSelected": "No participants selected", "participantsSelected": "{{count}} participants selected", "addParticipants": "Add Participants", "removeParticipants": "Remove Participants", "channelCreated": "Channel created successfully", "channelDeleted": "Channel deleted successfully", "participantsAdded": "Participants added successfully", "participantsRemoved": "Participants removed successfully", "failedToCreateChannel": "Failed to create channel", "failedToDeleteChannel": "Failed to delete channel", "failedToUpdateParticipants": "Failed to update participants", "customChannel": "Custom Channel", "generalChannel": "General Channel", "onlyCreatorCanDelete": "Only the channel creator can delete this channel", "cannotDeleteGeneral": "Cannot delete the general channel", "you": "You", "failedToLoadParticipants": "Failed to load participants", "noParticipantsAvailable": "No participants available", "failedToSendMessage": "Failed to send message"}, "briefs": {"searchPlaceholder": "Search briefs...", "filterByAccess": "Filter by access", "allBriefs": "All Briefs", "allParticipants": "All Participants", "contentCreators": "Content Creators", "reviewersAndAdmins": "Reviewers & Admins", "adminsOnly": "Admins Only", "createBrief": "Create Brief", "noBriefs": "No briefs yet", "noBriefsDescription": "Create your first brief to share guidelines, workflows, and important information with your team.", "createFirstBrief": "Create Your First Brief", "updated": "Updated", "created": "Created", "accessLevels": {"all": "All Participants", "creators": "Content Creators", "reviewers": "Reviewers & Admins", "admins": "Admins Only"}, "dialog": {"createTitle": "Create Brief", "editTitle": "<PERSON> Brief", "titleLabel": "Title", "titlePlaceholder": "Enter brief title...", "bodyLabel": "Content", "bodyPlaceholder": "Enter brief content...", "scopeLabel": "Who can access this brief?", "scopeDescription": "Choose who can view and access this brief", "specificPeopleLabel": "Additional People", "specificPeopleDescription": "Select specific people who can access this brief (optional)", "cancel": "Cancel", "save": "Save Brief", "creating": "Creating...", "updating": "Updating...", "createSuccess": "Brief created successfully", "updateSuccess": "Brief updated successfully"}, "viewDialog": {"title": "Brief Details", "loading": "Loading brief...", "error": "Failed to load brief details", "close": "Close", "basicInfo": "Basic Information", "content": "Content", "creatorInfo": "Creator Information", "createdBy": "Created by", "createdAt": "Created", "updatedAt": "Last updated", "noContent": "No content provided"}, "scopes": {"allParticipants": "All Participants", "adminsReviewers": "Admins & Reviewers Only", "adminsOnly": "Admins Only", "customSelection": "Custom Selection"}, "validation": {"titleRequired": "Brief title is required", "titleMaxLength": "Brief title must not exceed 255 characters", "bodyMaxLength": "Brief content must not exceed 10,000 characters"}, "actions": {"edit": "<PERSON> Brief", "delete": "Delete Brief", "confirmDelete": "Are you sure you want to delete this brief?", "confirmDeleteDescription": "This action cannot be undone. The brief will be permanently removed.", "deleteSuccess": "Brief deleted successfully"}}, "overview": {"totalPosts": "Total Posts", "pendingReviews": "Pending Reviews", "approved": "Approved", "participants": "Participants", "approvalRate": "Approval Rate", "contentApproval": "Content Approval", "approvedOutOf": "{{approved}} approved out of {{total}} reviewed", "participationRate": "Participation Rate", "activeParticipants": "Active Participants", "activeOutOf": "{{active}} active out of {{total}} total", "recentActivity": "Recent Activity", "messagesSent": "Messages Sent", "briefsCreated": "Briefs Created", "needsRework": "Needs Rework", "justNow": "Just now", "hoursAgo": "{{hours}}h ago", "daysAgo": "{{days}}d ago", "activities": {"created": "created a new post", "approved": "approved", "commentedOn": "commented on", "joinedHub": "joined the hub", "updated": "updated"}}, "mediaCarousel": {"video": "Video", "mediaAlt": "Media {{index}}"}, "manageParticipants": {"title": "Manage Participants", "description": "Invite new participants, update roles, and manage access to {{hubName}}.", "inviteTab": "Invite", "manageTab": "Manage ({{count}})", "participantType": "Participant Type", "externalUser": "External User", "brandContact": "Brand Contact", "emailAddress": "Email Address", "name": "Name", "nameOptional": "Name (Optional)", "selectContact": "Select contact...", "loadingContacts": "Loading contacts...", "searchContacts": "Search contacts...", "noContactsFound": "No contacts found.", "noContactsForBrand": "No contacts found for {{brandName}}.", "role": "Role", "selectRole": "Select a role", "externalInviteInfo": "An invitation email with a magic link will be sent to the participant. They can access the hub without creating an account.", "brandContactInviteInfo": "An invitation email with a magic link will be sent to the selected contact {{brandName}}. They can access the hub without creating an account.", "sendInvitation": "Send Invitation", "sendingInvitation": "Sending Invitation...", "cancel": "Cancel", "close": "Close", "noParticipants": "No participants yet", "noParticipantsDescription": "Invite participants to start collaborating on this hub.", "inviteParticipants": "Invite Participants", "loadingParticipants": "Loading participants...", "external": "External", "makeAdmin": "Make Admin", "contentCreator": "Content Creator", "reviewer": "Reviewer", "reviewerCreator": "Reviewer & Creator", "resendInvitation": "Resend Invitation", "remove": "Remove", "managingCount": "({{count}})"}}, "common": {"loading": "Loading...", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "retry": "Retry", "deleting": "Deleting...", "close": "Close", "clearAll": "Clear All", "tryAgain": "Try Again"}, "ui": {"emojiPicker": {"addEmoji": "Add emoji", "categories": {"smileysAndPeople": "Smileys & People", "animalsAndNature": "Animals & Nature", "foodAndDrink": "Food & Drink", "activities": "Activities", "travelAndPlaces": "Travel & Places", "objects": "Objects", "symbols": "Symbols"}}, "textareaWithEmoji": {"emojiInserted": "Emoji inserted"}, "reviewerMultiSelect": {"selectReviewers": "Select reviewers...", "searchReviewers": "Search reviewers...", "loadingReviewers": "Loading reviewers...", "noReviewersFound": "No reviewers found.", "noReviewersAvailable": "No reviewers available.", "reviewersSelected": "reviewers selected", "reviewer": "reviewer", "reviewers": "reviewers"}, "mentionInput": {"loadingParticipants": "Loading participants...", "noParticipantsFound": "No participants found"}, "userProfilePopup": {"unknownUser": "Unknown User", "externalUser": "External User", "sendEmail": "Send Email"}}, "navigation": {"userProfile": {"logout": "Log out", "loggingOut": "Logging out..."}}, "settings": {"title": "Settings", "description": "Manage your account settings and preferences.", "profile": {"title": "Profile", "description": "Your account information and profile details.", "name": "Name", "email": "Email", "accountType": "Account Type", "internalUser": "Internal User", "externalParticipant": "External Participant", "multiAccountAccess": "Multi-account access", "notSet": "Not set"}, "notifications": {"title": "Notification Preferences", "description": "Control how you receive notifications.", "externalDescription": "As an external participant, you can only receive email notifications.", "savingChanges": "Saving changes...", "categories": {"collaboration": "Collaboration", "content": "Content & Reviews", "communication": "Communication"}, "types": {"inviteToHub": {"label": "Hub Invitations", "description": "When you're invited to join a collaboration hub"}, "assignedAsReviewer": {"label": "Review Assignments", "description": "When you're assigned to review content"}, "postReviewed": {"label": "Content Reviews", "description": "When your content has been reviewed"}, "commentAdded": {"label": "New Comments", "description": "When someone comments on your content"}, "commentMention": {"label": "Comment Mentions", "description": "When you're mentioned in a comment"}, "chatMention": {"label": "Chat Mentions", "description": "When you're mentioned in a chat"}, "chatAdded": {"label": "Chat Invitations", "description": "When you're added to a chat channel"}}, "channels": {"inApp": {"label": "In-App", "description": "Notifications within the app"}, "email": {"label": "Email", "description": "Email notifications"}}, "badges": {"type": "type", "types": "types"}}}}