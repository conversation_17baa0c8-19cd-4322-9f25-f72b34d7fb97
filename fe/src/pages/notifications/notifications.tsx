import { useState, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import { Bell, Filter, Check, CheckCheck, Calendar, Tag } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useNotifications, useMarkAllNotificationsAsRead } from '@/hooks/use-notifications'
import { useNotificationNavigation } from '@/lib/notification-navigation'
import { formatDistanceToNow } from 'date-fns'
import { toast } from 'sonner'
import type { components } from '@/lib/api/v1'
import { NotificationResponseStatus, NotificationResponseType } from '@/lib/api/v1'

type NotificationResponse = components['schemas']['NotificationResponse']

const NOTIFICATION_TYPE_LABELS: Record<NotificationResponseType, string> = {
  [NotificationResponseType.CHAT_MENTION]: 'Chat Mention',
  [NotificationResponseType.CHAT_ADDED]: 'Added to Chat',
  [NotificationResponseType.COMMENT_ADDED]: 'New Comment',
  [NotificationResponseType.COMMENT_MENTION]: 'Comment Mention',
  [NotificationResponseType.POST_REVIEWED]: 'Post Reviewed',
  [NotificationResponseType.ASSIGNED_AS_REVIEWER]: 'Assigned as Reviewer',
  [NotificationResponseType.BRIEF_CREATED]: 'Brief Created',
  [NotificationResponseType.BRIEF_UPDATED]: 'Brief Updated',
  [NotificationResponseType.BRIEF_ASSIGNED]: 'Brief Assigned',
  [NotificationResponseType.INVITE_TO_HUB]: 'Hub Invitation',
}

const NOTIFICATION_TYPE_COLORS: Record<NotificationResponseType, string> = {
  [NotificationResponseType.CHAT_MENTION]: 'bg-blue-100 text-blue-800',
  [NotificationResponseType.CHAT_ADDED]: 'bg-blue-100 text-blue-800',
  [NotificationResponseType.COMMENT_ADDED]: 'bg-green-100 text-green-800',
  [NotificationResponseType.COMMENT_MENTION]: 'bg-green-100 text-green-800',
  [NotificationResponseType.POST_REVIEWED]: 'bg-purple-100 text-purple-800',
  [NotificationResponseType.ASSIGNED_AS_REVIEWER]: 'bg-orange-100 text-orange-800',
  [NotificationResponseType.BRIEF_CREATED]: 'bg-indigo-100 text-indigo-800',
  [NotificationResponseType.BRIEF_UPDATED]: 'bg-indigo-100 text-indigo-800',
  [NotificationResponseType.BRIEF_ASSIGNED]: 'bg-indigo-100 text-indigo-800',
  [NotificationResponseType.INVITE_TO_HUB]: 'bg-pink-100 text-pink-800',
}

export function NotificationsPage() {
  const [searchParams, setSearchParams] = useSearchParams()
  const [page, setPage] = useState(0)
  
  // Filter states from URL parameters
  const statusFilter = searchParams.get('status') as 'all' | 'unread' | 'read' || 'all'
  const typeFilter = searchParams.get('type') as NotificationResponseType | 'all' || 'all'
  
  const { navigateToNotification, markAsReadOnly } = useNotificationNavigation()
  const markAllAsRead = useMarkAllNotificationsAsRead()
  
  // Fetch notifications with filters
  const { data: notificationsResponse, isLoading, error } = useNotifications(page, 20, false)
  
  const notifications = notificationsResponse?.content || []
  const totalPages = notificationsResponse?.total_pages || 0
  const hasNextPage = notificationsResponse?.has_next || false
  const hasPreviousPage = notificationsResponse?.has_previous || false
  
  // Client-side filtering (until backend supports filtering)
  const filteredNotifications = useMemo(() => {
    let filtered: NotificationResponse[] = notifications
    
    if (statusFilter !== 'all') {
      filtered = filtered.filter(notification => 
        statusFilter === 'unread' 
          ? notification.status === NotificationResponseStatus.UNREAD
          : notification.status === NotificationResponseStatus.READ
      )
    }
    
    if (typeFilter !== 'all') {
      filtered = filtered.filter(notification => notification.type === typeFilter)
    }
    
    return filtered
  }, [notifications, statusFilter, typeFilter])
  
  const unreadCount = notifications.filter(n => n.status === NotificationResponseStatus.UNREAD).length
  
  const updateFilter = (key: string, value: string) => {
    const newParams = new URLSearchParams(searchParams)
    if (value === 'all') {
      newParams.delete(key)
    } else {
      newParams.set(key, value)
    }
    setSearchParams(newParams)
  }
  
  const handleMarkAllAsRead = () => {
    markAllAsRead.mutate(undefined, {
      onSuccess: () => {
        toast.success('All notifications marked as read')
      },
      onError: () => {
        toast.error('Failed to mark all notifications as read')
      }
    })
  }
  
  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Failed to load notifications</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Notifications</h1>
          <p className="text-muted-foreground mt-1">
            Stay updated with your collaboration activities
          </p>
        </div>
        
        {unreadCount > 0 && (
          <Button onClick={handleMarkAllAsRead} disabled={markAllAsRead.isPending}>
            <CheckCheck className="h-4 w-4 mr-2" />
            Mark all as read ({unreadCount})
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Status Filter */}
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select value={statusFilter} onValueChange={(value) => updateFilter('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Notifications</SelectItem>
                  <SelectItem value="unread">Unread Only</SelectItem>
                  <SelectItem value="read">Read Only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Type Filter */}
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">Type</label>
              <Select value={typeFilter} onValueChange={(value) => updateFilter('type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {Object.entries(NOTIFICATION_TYPE_LABELS).map(([type, label]) => (
                    <SelectItem key={type} value={type}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>
              {filteredNotifications.length} notification{filteredNotifications.length !== 1 ? 's' : ''}
            </span>
            {statusFilter !== 'all' && (
              <Badge variant="secondary">{statusFilter}</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Bell className="h-8 w-8 text-muted-foreground mx-auto mb-2 animate-pulse" />
                <p className="text-sm text-muted-foreground">Loading notifications...</p>
              </div>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No notifications found</p>
                <p className="text-sm text-muted-foreground mt-1">
                  {statusFilter !== 'all' || typeFilter !== 'all' 
                    ? 'Try adjusting your filters'
                    : 'You\'re all caught up!'
                  }
                </p>
              </div>
            </div>
          ) : (
            <ScrollArea className="h-[600px]">
              <div className="divide-y">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-muted/50 cursor-pointer transition-colors ${
                      notification.status === NotificationResponseStatus.UNREAD ? 'bg-blue-50/50' : ''
                    }`}
                    onClick={() => navigateToNotification(notification)}
                  >
                    <div className="flex items-start gap-3">
                      {/* Avatar */}
                      <Avatar className="h-10 w-10 flex-shrink-0">
                        <AvatarImage src="" />
                        <AvatarFallback className="text-xs">
                          {getInitials(notification.title)}
                        </AvatarFallback>
                      </Avatar>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className={`font-medium text-sm ${
                                notification.status === NotificationResponseStatus.UNREAD 
                                  ? 'text-foreground' 
                                  : 'text-muted-foreground'
                              }`}>
                                {notification.title}
                              </h3>
                              {notification.status === NotificationResponseStatus.UNREAD && (
                                <div className="h-2 w-2 bg-blue-500 rounded-full flex-shrink-0" />
                              )}
                            </div>
                            
                            <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                              {notification.message}
                            </p>
                            
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              <span>
                                {formatDistanceToNow(new Date(notification.createdAt!), { addSuffix: true })}
                              </span>
                              <Badge
                                variant="secondary"
                                className={`text-xs ${notification.type ? NOTIFICATION_TYPE_COLORS[notification.type] : 'bg-gray-100 text-gray-800'}`}
                              >
                                <Tag className="h-3 w-3 mr-1" />
                                {notification.type ? NOTIFICATION_TYPE_LABELS[notification.type] : 'Unknown'}
                              </Badge>
                            </div>
                          </div>

                          {/* Mark as read button */}
                          {notification.status === NotificationResponseStatus.UNREAD && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                markAsReadOnly(notification)
                              }}
                              className="h-auto p-1 flex-shrink-0 hover:bg-muted"
                              title="Mark as read"
                            >
                              <Check className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6">
          <Button
            variant="outline"
            onClick={() => setPage(page - 1)}
            disabled={!hasPreviousPage}
          >
            Previous
          </Button>
          
          <span className="text-sm text-muted-foreground">
            Page {page + 1} of {totalPages}
          </span>
          
          <Button
            variant="outline"
            onClick={() => setPage(page + 1)}
            disabled={!hasNextPage}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
