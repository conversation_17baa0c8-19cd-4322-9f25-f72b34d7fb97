# Unified Notification System

## Overview

The Collaboration Hub notification system has been completely unified to support both **internal users** (with user_id) and **external users** (email-only hub participants) through a single, production-grade system with full feature parity.

## Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────────────┐
│                    UNIFIED NOTIFICATION SYSTEM                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌──────────────────────────────────┐   │
│  │ Mixed Recipients │    │     NotificationDispatcherService │   │
│  │                 │───▶│                                  │   │
│  │ • Internal Users │    │ • Mixed recipient support        │   │
│  │ • External Users │    │ • Unified dispatch logic        │   │
│  └─────────────────┘    │ • Feature parity                │   │
│                         └──────────────────────────────────┘   │
│                                        │                       │
│                         ┌──────────────▼──────────────┐        │
│                         │    NotificationStorageService │        │
│                         │                             │        │
│                         │ • Mixed notification storage │        │
│                         │ • Database schema support   │        │
│                         └──────────────┬──────────────┘        │
│                                        │                       │
│                         ┌──────────────▼──────────────┐        │
│                         │  NotificationBatchingService │        │
│                         │                             │        │
│                         │ • External user batching    │        │
│                         │ • Urgency-based processing  │        │
│                         └─────────────────────────────┘        │
└─────────────────────────────────────────────────────────────────┘
```

### Database Schema

#### Updated Tables

**notification table:**
```sql
-- Mixed recipient support
user_id BIGINT NULL REFERENCES "user"(id),     -- Internal users
email VARCHAR(255) NULL,                       -- External users

-- Constraint: exactly one identifier required
CONSTRAINT chk_notification_recipient_type CHECK (
    (user_id IS NOT NULL AND email IS NULL) OR 
    (user_id IS NULL AND email IS NOT NULL)
);
```

**notification_batch_queue table:**
```sql
-- Mixed recipient support
user_id BIGINT NULL REFERENCES "user"(id),     -- Internal users  
email VARCHAR(255) NULL,                       -- External users

-- Constraint: exactly one identifier required
CONSTRAINT chk_batch_queue_recipient_type CHECK (
    (user_id IS NOT NULL AND email IS NULL) OR 
    (user_id IS NULL AND email IS NOT NULL)
);
```

## Recipient Types

### NotificationRecipient Interface

```java
public interface NotificationRecipient {
    boolean isInternal();
    String getEmail();
    String getDisplayName();
    Long getUserId(); // throws UnsupportedOperationException for external users
    String getUniqueIdentifier(); // "user:123" or "email:<EMAIL>"
}
```

### InternalUserRecipient

```java
// For users in the user table
InternalUserRecipient recipient = InternalUserRecipient.of(123L, "<EMAIL>", "John Doe");
```

### ExternalUserRecipient

```java
// For hub participants without user accounts
ExternalUserRecipient recipient = ExternalUserRecipient.of("<EMAIL>", "Jane Smith");
```

## API Usage

### Unified Dispatch

```java
// Mixed recipients (internal + external)
List<NotificationRecipient> recipients = List.of(
    InternalUserRecipient.of(123L, "<EMAIL>", "John Doe"),
    ExternalUserRecipient.of("<EMAIL>", "Jane Smith")
);

notificationDispatcherService.dispatchMixedNotification(
    NotificationType.POST_COMMENT_ADDED,
    "New comment on your post",
    "John Doe commented on your post 'Summer Campaign'",
    recipients,
    entityReferences,
    metadata
);
```

### Legacy Support (Internal Users Only)

```java
// Still supported for backward compatibility
List<Long> userIds = List.of(123L, 456L);

notificationDispatcherService.dispatchNotification(
    NotificationType.POST_COMMENT_ADDED,
    "New comment on your post", 
    "Someone commented on your post",
    userIds,
    entityReferences,
    metadata
);
```

## Feature Parity

| Feature | Internal Users | External Users | Implementation |
|---------|---------------|----------------|----------------|
| **In-App Notifications** | ✅ Full support | ✅ Full support | Database storage with email identifier |
| **Email Notifications** | ✅ Full support | ✅ Full support | Same email service, different identification |
| **Email Batching** | ✅ Full support | ✅ Full support | Mixed batch queue with user_id/email |
| **Notification Preferences** | ✅ Full support | ✅ Full support | Email-based preference storage for both IN_APP and EMAIL |
| **Urgency Levels** | ✅ Full support | ✅ Full support | Same urgency processing logic |
| **Retry Logic** | ✅ Full support | ✅ Full support | Unified retry mechanism |
| **Deep Linking** | ✅ Full support | ✅ Full support | Entity references in all notifications |

## Controllers

### Unified Endpoints (Works for Both User Types)
- `GET /api/notifications` - Get notifications (automatically detects user type)
- `POST /api/notifications/mark-read` - Mark notification as read (internal users only)
- `POST /api/notifications/mark-all-read` - Mark all as read (works for both user types)
- `GET /api/notifications/unread-count` - Get unread count (works for both user types)

The unified controller automatically:
- Detects user type from JWT token (Internal vs External)
- Routes internal users to user ID-based service
- Routes external users to email-based service
- Returns consistent responses for both user types

## Security

### Internal Users
- JWT authentication with user_id
- Role-based access control
- Account-scoped data access

### External Users  
- JWT authentication with email claim
- EXTERNAL_PARTICIPANT role
- Hub-scoped access restrictions
- Email validation against authenticated context

## Performance Optimizations

### Database Indexes
```sql
-- External user optimized indexes
CREATE INDEX idx_notification_email ON notification(email) WHERE user_id IS NULL;
CREATE INDEX idx_notification_email_status ON notification(email, status) WHERE user_id IS NULL;
CREATE INDEX idx_batch_queue_email ON notification_batch_queue(email) WHERE user_id IS NULL;

-- Mixed recipient indexes
CREATE INDEX idx_notification_recipient_status ON notification(COALESCE(user_id::text, email), status);
```

### Bulk Operations
- `bulkCreateMixedNotifications()` - Batch insert for mixed recipients
- `bulkMarkAsRead()` - Batch updates with user_id or email
- N+1 query prevention with bulk loading

## Migration Strategy

### Phase 1: Database Schema ✅
- Updated notification tables with nullable user_id
- Added email columns with proper constraints
- Created performance indexes

### Phase 2: Service Unification ✅
- Created unified recipient classes
- Extended all services for mixed recipient support
- Maintained backward compatibility

### Phase 3: Controller Updates ✅
- Added external user endpoints
- Maintained existing internal user endpoints
- Proper authentication and authorization

### Phase 4: Cleanup ✅
- Removed deprecated NotificationEventService
- Unified all notification handling
- Comprehensive error handling and logging

## Production Readiness

### Code Quality
- ✅ Methods limited to 20-30 lines
- ✅ Comprehensive validation with @Valid annotations
- ✅ Proper @Transactional annotations (readOnly=true for queries)
- ✅ Constants extracted and organized
- ✅ SOLID/KISS/DRY principles applied

### Error Handling
- ✅ Specific exception types (NotificationNotFoundException, NotificationStorageException)
- ✅ Comprehensive logging with structured messages
- ✅ Proper HTTP status codes (401, 403, 404, 500)
- ✅ Validation error messages

### Performance
- ✅ Bulk operations with transaction rollback
- ✅ Optimized database queries with proper indexing
- ✅ jOOQ fetchInto() projections for type safety
- ✅ N+1 query prevention

### Documentation
- ✅ Comprehensive JavaDoc for all public methods
- ✅ OpenAPI annotations for all endpoints
- ✅ Clear parameter descriptions and examples
- ✅ Architecture documentation

The unified notification system now provides seamless support for both internal and external users with full feature parity, production-grade quality, and comprehensive validation throughout all layers.
