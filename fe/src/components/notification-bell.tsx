import { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>he<PERSON>, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { useNotifications, useUnreadNotificationCount, useMarkAllNotificationsAsRead } from '@/hooks/use-notifications';
import { useAuth } from '@/hooks/auth';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { useNotificationNavigation } from '@/lib/notification-navigation';
import type { components } from '@/lib/api/v1';
import { NotificationResponseStatus } from '@/lib/api/v1';

type NotificationResponse = components['schemas']['NotificationResponse'];

/**
 * Notification bell component for the header.
 * Shows unread count badge and dropdown with recent notifications.
 */
export default function NotificationBell() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  
  // Always call hooks first, then conditionally render
  const { data: unreadCount } = useUnreadNotificationCount();
  const { data: notifications, isLoading } = useNotifications(0, 10, false);
  const markAllAsRead = useMarkAllNotificationsAsRead();
  const { navigateToNotification, markAsReadOnly } = useNotificationNavigation();

  // Only show for internal users (external users don't get in-app notifications)
  if (!user?.internal) {
    return null;
  }

  const handleMarkAllAsRead = () => {
    markAllAsRead.mutate({});
  };

  const hasUnread = (unreadCount || 0) > 0;

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {hasUnread && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount! > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-80">
        <div className="flex items-center justify-between p-3">
          <DropdownMenuLabel className="font-semibold p-0">Notifications</DropdownMenuLabel>
          {hasUnread && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              disabled={markAllAsRead.isPending}
              className="h-auto p-1 text-xs"
            >
              <CheckCheck className="h-3 w-3 mr-1" />
              Mark all read
            </Button>
          )}
        </div>
        
        <DropdownMenuSeparator />
        
        <ScrollArea className="h-96">
          {isLoading ? (
            <NotificationsSkeleton />
          ) : notifications?.content?.length ? (
            notifications.content.map((notification: NotificationResponse) => (
              <div
                key={notification.id}
                className="flex flex-col items-start p-3 cursor-pointer hover:bg-accent border-b border-border last:border-b-0"
                onClick={(e) => {
                  // Prevent click if clicking on the mark-as-read button
                  if ((e.target as HTMLElement).closest('[data-mark-read-button]')) {
                    return;
                  }

                  navigateToNotification(notification);
                  setIsOpen(false); // Close dropdown after navigation
                }}
              >
                <div className="flex items-start justify-between w-full">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <p className={`text-sm font-medium truncate ${
                        notification.status === NotificationResponseStatus.UNREAD ? 'text-foreground' : 'text-muted-foreground'
                      }`}>
                        {notification.title}
                      </p>
                      {notification.status === NotificationResponseStatus.UNREAD && (
                        <div className="h-2 w-2 bg-blue-500 rounded-full flex-shrink-0" />
                      )}
                      <ExternalLink className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                    </div>
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {notification.message}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatDistanceToNow(new Date(notification.createdAt!), { addSuffix: true })}
                    </p>
                  </div>
                  {notification.status === NotificationResponseStatus.UNREAD && (
                    <Button
                      variant="ghost"
                      size="sm"
                      data-mark-read-button
                      onClick={(e) => {
                        e.stopPropagation();
                        markAsReadOnly(notification);
                      }}
                      className="h-auto p-1 ml-2 flex-shrink-0 hover:bg-muted"
                      title="Mark as read"
                    >
                      <Check className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-muted-foreground">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No notifications yet</p>
            </div>
          )}
        </ScrollArea>
        
        {notifications?.content?.length ? (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-center justify-center"
              onClick={() => {
                navigate('/app/notifications');
                setIsOpen(false);
              }}
            >
              <Button variant="ghost" size="sm" className="text-xs">
                View all notifications
              </Button>
            </DropdownMenuItem>
          </>
        ) : null}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

/**
 * Skeleton loader for notifications dropdown.
 */
function NotificationsSkeleton() {
  return (
    <div className="space-y-3 p-3">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="space-y-2">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-2 w-2 rounded-full" />
          </div>
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-20" />
        </div>
      ))}
    </div>
  );
}
